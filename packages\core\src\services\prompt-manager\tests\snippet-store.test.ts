import { describe, it, expect, beforeEach, mock } from "bun:test";
import { Lo<PERSON> } from "koishi";
import { SnippetStore } from "../snippet-store";
import { Snippet, RenderContext } from "../types";

// Mock Logger
const mockLogger = {
    info: mock(() => {}),
    warn: mock(() => {}),
    error: mock(() => {}),
    debug: mock(() => {}),
} as unknown as <PERSON><PERSON>;

describe("SnippetStore", () => {
    let snippetStore: SnippetStore;

    beforeEach(() => {
        snippetStore = new SnippetStore(mockLogger, 1000, 100); // 1秒缓存，最多100个条目
    });

    describe("片段注册和管理", () => {
        it("应该能注册片段", () => {
            const provider = (context: RenderContext) => "test value";
            const options = { description: "测试片段" };

            snippetStore.registerSnippet({ key: "test.snippet", provider, options });

            const retrieved = snippetStore.getSnippet("test.snippet");
            expect(retrieved).toEqual({
                key: "test.snippet",
                provider,
                options,
            });
        });

        it("应该能使用便捷方法注册片段", () => {
            const provider = () => "value";
            const options = { description: "test" };

            snippetStore.register("convenient", provider, options);

            const retrieved = snippetStore.getSnippet("convenient");
            expect(retrieved?.key).toBe("convenient");
            expect(retrieved?.provider).toBe(provider);
            expect(retrieved?.options).toBe(options);
        });

        it("应该能覆盖已存在的片段", () => {
            const provider1 = () => "value1";
            const provider2 = () => "value2";

            snippetStore.register("test", provider1);
            snippetStore.register("test", provider2);

            const retrieved = snippetStore.getSnippet("test");
            expect(retrieved?.provider).toBe(provider2);
        });

        it("应该能批量注册片段", () => {
            const snippets: Snippet[] = [
                { key: "snippet1", provider: () => "value1" },
                { key: "snippet2", provider: () => "value2" },
            ];

            snippetStore.registerSnippets(snippets);

            expect(snippetStore.hasSnippet("snippet1")).toBe(true);
            expect(snippetStore.hasSnippet("snippet2")).toBe(true);
        });

        it("应该能获取所有片段键名", () => {
            snippetStore.register("snippet1", () => "value1");
            snippetStore.register("snippet2", () => "value2");

            const keys = snippetStore.getSnippetKeys();
            expect(keys).toContain("snippet1");
            expect(keys).toContain("snippet2");
            expect(keys.length).toBe(2);
        });

        it("应该能获取所有片段", () => {
            const snippet1: Snippet = { key: "snippet1", provider: () => "value1" };
            const snippet2: Snippet = { key: "snippet2", provider: () => "value2" };

            snippetStore.registerSnippet(snippet1);
            snippetStore.registerSnippet(snippet2);

            const snippets = snippetStore.getAllSnippets();
            expect(snippets).toHaveLength(2);
            expect(snippets).toContainEqual(snippet1);
            expect(snippets).toContainEqual(snippet2);
        });

        it("应该能注销片段", () => {
            snippetStore.register("to-remove", () => "value");
            expect(snippetStore.hasSnippet("to-remove")).toBe(true);

            const result = snippetStore.unregisterSnippet("to-remove");
            expect(result).toBe(true);
            expect(snippetStore.hasSnippet("to-remove")).toBe(false);
        });

        it("注销不存在的片段应该返回 false", () => {
            const result = snippetStore.unregisterSnippet("not-exists");
            expect(result).toBe(false);
        });
    });

    describe("片段验证", () => {
        it("应该拒绝空键名的片段", () => {
            expect(() => {
                snippetStore.registerSnippet({ key: "", provider: () => "value" });
            }).toThrow("片段键名不能为空");
        });

        it("应该拒绝无效提供函数的片段", () => {
            expect(() => {
                snippetStore.registerSnippet({ key: "test", provider: "not a function" as any });
            }).toThrow("片段提供函数必须是一个函数");
        });
    });

    describe("片段执行", () => {
        it("应该能执行同步片段", async () => {
            snippetStore.register("sync", () => "sync value");

            const result = await snippetStore.executeSnippet("sync", {});

            expect(result.success).toBe(true);
            expect(result.value).toBe("sync value");
            expect(result.key).toBe("sync");
            expect(result.fromCache).toBe(false);
            expect(result.executionTime).toBeGreaterThan(0);
        });

        it("应该能执行异步片段", async () => {
            snippetStore.register("async", async () => {
                await new Promise((resolve) => setTimeout(resolve, 10));
                return "async value";
            });

            const result = await snippetStore.executeSnippet("async", {});

            expect(result.success).toBe(true);
            expect(result.value).toBe("async value");
            expect(result.fromCache).toBe(false);
        });

        it("应该能处理片段执行错误", async () => {
            snippetStore.register("error", () => {
                throw new Error("Test error");
            });

            const result = await snippetStore.executeSnippet("error", {});

            expect(result.success).toBe(false);
            expect(result.value).toBeUndefined();
            expect(result.error?.message).toBe("Test error");
        });

        it("应该能使用默认值处理错误", async () => {
            snippetStore.register(
                "error-with-default",
                () => {
                    throw new Error("Test error");
                },
                { defaultValue: "default value" }
            );

            const result = await snippetStore.executeSnippet("error-with-default", {});

            expect(result.success).toBe(true);
            expect(result.value).toBe("default value");
            expect(result.error).toBeDefined();
        });

        it("必需片段失败时应该返回失败结果", async () => {
            snippetStore.register(
                "required-error",
                () => {
                    throw new Error("Required error");
                },
                { required: true }
            );

            const result = await snippetStore.executeSnippet("required-error", {});

            expect(result.success).toBe(false);
            expect(result.value).toBeUndefined();
        });

        it("应该能处理不存在的片段", async () => {
            const result = await snippetStore.executeSnippet("not-exists", {});

            expect(result.success).toBe(false);
            expect(result.error?.message).toContain("片段 'not-exists' 不存在");
        });

        it("应该能处理超时", async () => {
            snippetStore.register("slow", async () => {
                await new Promise((resolve) => setTimeout(resolve, 100));
                return "slow value";
            });

            const result = await snippetStore.executeSnippet("slow", {}, true, 50);

            expect(result.success).toBe(false);
            expect(result.error?.message).toContain("超时");
        });

        it("应该能批量执行片段", async () => {
            snippetStore.register("snippet1", () => "value1");
            snippetStore.register("snippet2", () => "value2");

            const results = await snippetStore.executeSnippets(["snippet1", "snippet2"], {});

            expect(results).toHaveLength(2);
            expect(results[0].key).toBe("snippet1");
            expect(results[0].value).toBe("value1");
            expect(results[1].key).toBe("snippet2");
            expect(results[1].value).toBe("value2");
        });
    });

    describe("缓存管理", () => {
        it("应该能缓存片段结果", async () => {
            let callCount = 0;
            snippetStore.register(
                "cached",
                () => {
                    callCount++;
                    return "cached value";
                },
                { cacheTTL: 1000 }
            );

            // 第一次执行
            const result1 = await snippetStore.executeSnippet("cached", {});
            expect(result1.fromCache).toBe(false);
            expect(callCount).toBe(1);

            // 第二次执行应该使用缓存
            const result2 = await snippetStore.executeSnippet("cached", {});
            expect(result2.fromCache).toBe(true);
            expect(result2.value).toBe("cached value");
            expect(callCount).toBe(1); // 没有再次调用
        });

        it("应该能禁用特定片段的缓存", async () => {
            let callCount = 0;
            snippetStore.register(
                "no-cache",
                () => {
                    callCount++;
                    return "value";
                },
                { cacheTTL: 0 }
            );

            await snippetStore.executeSnippet("no-cache", {});
            await snippetStore.executeSnippet("no-cache", {});

            expect(callCount).toBe(2); // 每次都调用
        });

        it("应该能处理缓存过期", async () => {
            let callCount = 0;
            snippetStore.register(
                "expiring",
                () => {
                    callCount++;
                    return "value";
                },
                { cacheTTL: 10 }
            ); // 10ms 过期

            await snippetStore.executeSnippet("expiring", {});
            expect(callCount).toBe(1);

            // 等待缓存过期
            await new Promise((resolve) => setTimeout(resolve, 20));

            await snippetStore.executeSnippet("expiring", {});
            expect(callCount).toBe(2); // 缓存过期，重新执行
        });

        it("应该能清理过期缓存", async () => {
            snippetStore.register("expiring", () => "value", { cacheTTL: 1 });

            await snippetStore.executeSnippet("expiring", {});

            // 等待过期
            await new Promise((resolve) => setTimeout(resolve, 10));

            snippetStore.cleanupExpiredCache();

            // 验证清理不会抛出错误
            expect(() => snippetStore.cleanupExpiredCache()).not.toThrow();
        });

        it("应该能清除所有缓存", async () => {
            snippetStore.register("cached", () => "value");
            await snippetStore.executeSnippet("cached", {});

            snippetStore.clearAllCache();

            // 验证清除不会抛出错误
            expect(() => snippetStore.clearAllCache()).not.toThrow();
        });
    });
});

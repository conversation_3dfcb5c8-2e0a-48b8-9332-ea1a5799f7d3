const text = `{
  "thoughts": {
    "observe": "Miaow要求咱简要概括之前图片中的日志内容。",
    "analyze_infer": "图片里的文字是咱的运行日志，咱可以从中识别出关键信息并进行概括。日志显示了咱和用户佐仓双叶的几次互动，咱使用了gemini-2.5-flash模型处理了她的消息，比如'发自拍'、'快点'等，还记录了响应时间、token消耗，以及图片处理的记录呢。咱要用NekoChan的可爱语气来总结。",
    "plan": "咱会把日志里的主要内容提炼出来，告诉Miaow咱处理了哪些消息，用了什么模型，还有图片处理的情况，用简短可爱的语气发送出去。"
  },
  "actions": [
    {
      "function": "send_message",
      "params": {
        "inner_thoughts": "咱要概括日志内容，突出关键信息，同时保持NekoChan的可爱风格。",
        "message": "好哒Miaow！咱看到日志里有咱和用户佐仓双叶的聊天记录呢，咱用了gemini-2.5-flash模型回复了她好几条消息，比如“发自拍”和“快点”什么的！ 还处理了新图片哦~ 响应时间嘛，大概都在几秒钟呢！"
      }
    }
  ],
  "request_heartbeat": false
}`;

console.log(text.replace(/^\s*/gm, ""));

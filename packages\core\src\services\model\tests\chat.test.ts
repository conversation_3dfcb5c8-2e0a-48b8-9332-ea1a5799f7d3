import { readFile } from "fs/promises";
import { generateText } from "xsai";

(async () => {
    const image = await readFile("E:\\Pictures\\Camera\\Screenshot_20230411-163245_.png", "base64");
    const data = `data:image/png;base64,${image}`;

    const result = await generateText({
        baseURL: "https://gemini.maliya.top/v1beta/openai/",
        // baseURL: "https://generativelanguage.googleapis.com/v1beta/openai/",
        // baseURL: "https://api-proxy.me/gemini/v1beta/openai/",
        apiKey: "...",
        model: "gemini-2.5-flash",
        // fetch: (async (input, init) => {
        //     init = { ...init, dispatcher: new ProxyAgent("http://127.0.0.1:7897") };
        //     return ufetch(input, init);
        // }) as unknown as typeof globalThis.fetch,
        messages: [
            {
                role: "user",
                content: [
                    { type: "text", text: "这张图片里有什么" },
                    { type: "image_url", image_url: { url: data, detail: "low" } },
                    { type: "image_url", image_url: { url: data, detail: "low" } },
                    // { type: "image_url", image_url: { url: data, detail: "low" } },
                ],
            },
        ],
    });

    console.log(result.text);
})();
